import React, { useState } from 'react';
import {
  ArrowLeft,
  User,
  Shield,
  MoreVertical,
  Phone,
  Video,
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useWebRTC } from '../../hooks/useWebRTC';
// import { consultationAPI } from '../../services/api';
import { useToast } from '../../contexts/ToastContext';
// import VoiceCallInterface from '../calls/VoiceCallInterface';
// import VideoCallInterface from '../calls/VideoCallInterface';
// import ConsultationScheduleModal from '../calls/ConsultationScheduleModal';

const ChatHeader = ({ otherParticipant, isOnline, onBack, chatId }) => {
  const { user } = useAuth();
  const { startCall } = useWebRTC();
  const { success, error } = useToast();

  // const [showVoiceCall, setShowVoiceCall] = useState(false);
  // const [showVideoCall, setShowVideoCall] = useState(false);
  // const [showConsultationModal, setShowConsultationModal] = useState(false);

  // Handle voice call initiation
  const handleVoiceCall = async () => {
    if (!otherParticipant?.user?._id) {
      error('Cannot start call: User information not available');
      return;
    }

    try {
      console.log('Starting voice call with:', otherParticipant.user.name);
      await startCall(otherParticipant.user._id, 'voice', chatId);
      success('Voice call initiated (demo mode)');
    } catch (err) {
      console.error('Failed to start voice call:', err);
      error('Failed to start voice call');
    }
  };

  // Handle video call/consultation request
  const handleVideoCall = async () => {
    if (!otherParticipant?.user?._id) {
      error('Cannot start consultation: User information not available');
      return;
    }

    try {
      console.log('Starting video consultation with:', otherParticipant.user.name);
      await startCall(otherParticipant.user._id, 'video', chatId);
      success('Video consultation initiated (demo mode)');
    } catch (err) {
      console.error('Failed to start video consultation:', err);
      error('Failed to start video consultation');
    }
  };

  // Handle consultation scheduling
  const handleScheduleConsultation = async (consultationData) => {
    // Temporarily disabled
    console.log('Consultation scheduling temporarily disabled');
    /*
    try {
      const response = await consultationAPI.createRequest(consultationData);
      if (response.success) {
        success('Consultation request sent successfully');
        setShowConsultationModal(false);
      } else {
        error(response.error || 'Failed to send consultation request');
      }
    } catch (err) {
      console.error('Failed to schedule consultation:', err);
      error('Failed to schedule consultation');
    }
    */
  };

  // Check if user can make calls (both users should be online for voice calls)
  const canMakeVoiceCall = isOnline && otherParticipant?.user?._id;

  // Video consultations can be requested even if the other user is offline
  const canRequestConsultation = otherParticipant?.user?._id &&
                                 otherParticipant?.user?.role === 'lawyer';

  return (
    <div className="bg-white border-b border-gray-200 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {onBack && (
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}

          {/* Avatar */}
          <div className="relative">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              {otherParticipant?.user.role === 'lawyer' ? (
                <Shield className="h-5 w-5 text-white" />
              ) : (
                <User className="h-5 w-5 text-white" />
              )}
            </div>
            {/* Online Status */}
            {isOnline && (
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
            )}
          </div>

          {/* User Info */}
          <div>
            <h3 className="font-semibold text-gray-900">
              {otherParticipant?.user.name || 'Unknown User'}
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span className="capitalize">{otherParticipant?.user.role}</span>
              {otherParticipant?.user.role === 'lawyer' && 
               otherParticipant?.user.lawyerDetails?.specialization && (
                <>
                  <span>•</span>
                  <span>{otherParticipant.user.lawyerDetails.specialization.join(', ')}</span>
                </>
              )}
            </div>
            <div className="text-xs text-gray-400">
              {isOnline ? 'Online' : 'Offline'}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          {/* Voice Call Button */}
          <button
            onClick={handleVoiceCall}
            disabled={!canMakeVoiceCall}
            className={`p-2 rounded-full transition-colors ${
              canMakeVoiceCall
                ? 'hover:bg-gray-100 text-gray-600'
                : 'text-gray-400 cursor-not-allowed'
            }`}
            title={canMakeVoiceCall ? 'Start voice call' : 'User is offline'}
          >
            <Phone className="h-5 w-5" />
          </button>

          {/* Video Call/Consultation Button */}
          <button
            onClick={handleVideoCall}
            disabled={!canRequestConsultation}
            className={`p-2 rounded-full transition-colors ${
              canRequestConsultation
                ? 'hover:bg-gray-100 text-gray-600'
                : 'text-gray-400 cursor-not-allowed'
            }`}
            title={
              canRequestConsultation
                ? 'Request video consultation'
                : 'Video consultations only available with lawyers'
            }
          >
            <Video className="h-5 w-5" />
          </button>

          {/* More Options Button */}
          <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
            <MoreVertical className="h-5 w-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Call Interfaces and Modals - Temporarily disabled */}
      {/*
      <VoiceCallInterface
        isOpen={showVoiceCall}
        onClose={() => setShowVoiceCall(false)}
        targetUser={otherParticipant?.user}
        chatId={chatId}
      />

      <VideoCallInterface
        isOpen={showVideoCall}
        onClose={() => setShowVideoCall(false)}
        targetUser={otherParticipant?.user}
        chatId={chatId}
      />

      <ConsultationScheduleModal
        isOpen={showConsultationModal}
        onClose={() => setShowConsultationModal(false)}
        targetUser={otherParticipant?.user}
        onSchedule={handleScheduleConsultation}
      />
      */}
    </div>
  );
};

export default ChatHeader;
