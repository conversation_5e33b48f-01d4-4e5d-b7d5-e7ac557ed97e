import Peer from 'simple-peer';
import { v4 as uuidv4 } from 'uuid';

class WebRTCService {
  constructor() {
    this.peer = null;
    this.localStream = null;
    this.remoteStream = null;
    this.socket = null;
    this.isInitiator = false;
    this.callId = null;
    this.callType = null; // 'voice' or 'video'
    this.onRemoteStream = null;
    this.onCallEnd = null;
    this.onError = null;
    this.onConnectionStateChange = null;
    this.onIncomingCall = null;
  }

  // Initialize WebRTC service with socket connection
  initialize(socket) {
    this.socket = socket;
    this.setupSocketListeners();
  }

  // Setup socket event listeners for WebRTC signaling
  setupSocketListeners() {
    if (!this.socket) return;

    // Listen for incoming call offers
    this.socket.on('webrtc_offer', this.handleOffer.bind(this));
    
    // Listen for call answers
    this.socket.on('webrtc_answer', this.handleAnswer.bind(this));
    
    // Listen for ICE candidates
    this.socket.on('webrtc_ice_candidate', this.handleIceCandidate.bind(this));
    
    // Listen for call end
    this.socket.on('webrtc_call_end', this.handleCallEnd.bind(this));
    
    // Listen for call rejection
    this.socket.on('webrtc_call_rejected', this.handleCallRejected.bind(this));

    // Listen for WebRTC signals
    this.socket.on('webrtc_signal', this.handleSignal.bind(this));
  }

  // Start a call (voice or video)
  async startCall(targetUserId, callType = 'voice', chatId = null) {
    try {
      console.log('🚀 WebRTC: Starting call...', { targetUserId, callType, chatId });

      // Check if socket is initialized
      if (!this.socket) {
        throw new Error('WebRTC service not initialized - socket is missing');
      }

      // Check if socket is connected
      if (!this.socket.connected) {
        throw new Error('Socket is not connected');
      }

      this.callId = uuidv4();
      this.callType = callType;
      this.isInitiator = true;

      // Check if getUserMedia is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('WebRTC is not supported in this browser');
      }

      // Get user media
      const constraints = {
        audio: true,
        video: callType === 'video'
      };

      console.log('🎥 WebRTC: Requesting media permissions...', constraints);
      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('✅ WebRTC: Media permissions granted');

      // Create peer connection
      console.log('🔗 WebRTC: Creating peer connection...');
      this.peer = new Peer({
        initiator: true,
        trickle: false,
        stream: this.localStream,
        config: {
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ]
        }
      });

      this.setupPeerListeners();

      // Send call offer via socket
      console.log('📡 WebRTC: Sending call offer via socket...');
      this.socket.emit('webrtc_call_offer', {
        callId: this.callId,
        targetUserId,
        callType,
        chatId
      });

      console.log('✅ WebRTC: Call initiated successfully, callId:', this.callId);
      return this.callId;
    } catch (error) {
      console.error('❌ WebRTC: Error starting call:', error);
      this.cleanup();
      if (this.onError) this.onError(error);
      throw error;
    }
  }

  // Answer an incoming call
  async answerCall(callData) {
    try {
      this.callId = callData.callId;
      this.callType = callData.callType;
      this.isInitiator = false;

      // Get user media
      const constraints = {
        audio: true,
        video: this.callType === 'video'
      };

      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      
      // Create peer connection
      this.peer = new Peer({
        initiator: false,
        trickle: false,
        stream: this.localStream,
        config: {
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ]
        }
      });

      this.setupPeerListeners();

      // Send answer via socket
      this.socket.emit('webrtc_call_answer', {
        callId: this.callId,
        targetUserId: callData.fromUserId
      });

      return this.callId;
    } catch (error) {
      console.error('Error answering call:', error);
      if (this.onError) this.onError(error);
      throw error;
    }
  }

  // Reject an incoming call
  rejectCall(callData) {
    this.socket.emit('webrtc_call_reject', {
      callId: callData.callId,
      targetUserId: callData.fromUserId
    });
  }

  // End the current call
  endCall() {
    if (this.callId && this.socket) {
      this.socket.emit('webrtc_call_end', {
        callId: this.callId
      });
    }
    this.cleanup();
  }

  // Setup peer connection event listeners
  setupPeerListeners() {
    if (!this.peer) return;

    this.peer.on('signal', (data) => {
      if (this.socket && this.callId) {
        this.socket.emit('webrtc_signal', {
          callId: this.callId,
          signal: data
        });
      }
    });

    this.peer.on('stream', (stream) => {
      this.remoteStream = stream;
      if (this.onRemoteStream) {
        this.onRemoteStream(stream);
      }
    });

    this.peer.on('connect', () => {
      console.log('WebRTC peer connected');
      if (this.onConnectionStateChange) {
        this.onConnectionStateChange('connected');
      }
    });

    this.peer.on('close', () => {
      console.log('WebRTC peer connection closed');
      this.cleanup();
    });

    this.peer.on('error', (error) => {
      console.error('WebRTC peer error:', error);
      if (this.onError) this.onError(error);
      this.cleanup();
    });
  }

  // Handle incoming call offer
  handleOffer(data) {
    // This will be handled by the UI components
    console.log('Received call offer:', data);
    if (this.onIncomingCall) {
      this.onIncomingCall(data);
    }
  }

  // Handle call answer
  handleAnswer(data) {
    console.log('Call answered:', data);
    if (data.signal && this.peer) {
      this.peer.signal(data.signal);
    }
  }

  // Handle WebRTC signal
  handleSignal(data) {
    if (this.peer && data.signal) {
      this.peer.signal(data.signal);
    }
  }

  // Handle ICE candidate
  handleIceCandidate(data) {
    if (this.peer && data.candidate) {
      this.peer.signal(data.candidate);
    }
  }

  // Handle call end
  handleCallEnd(data) {
    console.log('Call ended:', data);
    this.cleanup();
    if (this.onCallEnd) {
      this.onCallEnd();
    }
  }

  // Handle call rejection
  handleCallRejected(data) {
    console.log('Call rejected:', data);
    this.cleanup();
    if (this.onCallEnd) {
      this.onCallEnd('rejected');
    }
  }

  // Toggle audio mute
  toggleAudio() {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        return audioTrack.enabled;
      }
    }
    return false;
  }

  // Toggle video mute
  toggleVideo() {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        return videoTrack.enabled;
      }
    }
    return false;
  }

  // Get local stream
  getLocalStream() {
    return this.localStream;
  }

  // Get remote stream
  getRemoteStream() {
    return this.remoteStream;
  }

  // Cleanup resources
  cleanup() {
    if (this.peer) {
      this.peer.destroy();
      this.peer = null;
    }

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    this.remoteStream = null;
    this.callId = null;
    this.callType = null;
    this.isInitiator = false;

    if (this.onConnectionStateChange) {
      this.onConnectionStateChange('disconnected');
    }
  }

  // Set event handlers
  setEventHandlers({ onRemoteStream, onCallEnd, onError, onConnectionStateChange, onIncomingCall }) {
    this.onRemoteStream = onRemoteStream;
    this.onCallEnd = onCallEnd;
    this.onError = onError;
    this.onConnectionStateChange = onConnectionStateChange;
    this.onIncomingCall = onIncomingCall;
  }
}

// Create singleton instance
const webrtcService = new WebRTCService();

export default webrtcService;
