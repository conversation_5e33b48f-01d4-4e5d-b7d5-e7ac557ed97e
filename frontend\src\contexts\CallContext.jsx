import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useSocket } from './SocketContext';
import { useWebRTC } from '../hooks/useWebRTC';

// Call States
export const CALL_STATES = {
  IDLE: 'idle',
  OUTGOING: 'outgoing',
  INCOMING: 'incoming', 
  ACTIVE: 'active',
  ENDED: 'ended'
};

// Call Actions
const CALL_ACTIONS = {
  SET_CALL_STATE: 'SET_CALL_STATE',
  SET_INCOMING_CALL: 'SET_INCOMING_CALL',
  SET_ACTIVE_CALL: 'SET_ACTIVE_CALL',
  SET_CALL_CONTROLS: 'SET_CALL_CONTROLS',
  CLEAR_CALL: 'CLEAR_CALL',
  SET_PARTICIPANT_INFO: 'SET_PARTICIPANT_INFO'
};

// Initial State
const initialState = {
  callState: CALL_STATES.IDLE,
  currentCall: null,
  incomingCall: null,
  participantInfo: null,
  callControls: {
    isMuted: false,
    isVideoEnabled: true,
    isSpeakerOn: false
  }
};

// Reducer
const callReducer = (state, action) => {
  switch (action.type) {
    case CALL_ACTIONS.SET_CALL_STATE:
      return {
        ...state,
        callState: action.payload
      };
    
    case CALL_ACTIONS.SET_INCOMING_CALL:
      return {
        ...state,
        callState: CALL_STATES.INCOMING,
        incomingCall: action.payload.callData,
        participantInfo: action.payload.participantInfo
      };
    
    case CALL_ACTIONS.SET_ACTIVE_CALL:
      return {
        ...state,
        callState: CALL_STATES.ACTIVE,
        currentCall: action.payload.callData,
        incomingCall: null,
        participantInfo: action.payload.participantInfo
      };
    
    case CALL_ACTIONS.SET_CALL_CONTROLS:
      return {
        ...state,
        callControls: {
          ...state.callControls,
          ...action.payload
        }
      };
    
    case CALL_ACTIONS.SET_PARTICIPANT_INFO:
      return {
        ...state,
        participantInfo: action.payload
      };
    
    case CALL_ACTIONS.CLEAR_CALL:
      return {
        ...initialState
      };
    
    default:
      return state;
  }
};

// Context
const CallContext = createContext();

// Provider Component
export const CallProvider = ({ children }) => {
  const [state, dispatch] = useReducer(callReducer, initialState);
  const { socket } = useSocket();
  const webRTC = useWebRTC();

  // Socket Event Handlers
  useEffect(() => {
    if (!socket) return;

    // Handle incoming call offer
    const handleIncomingCall = (callData) => {
      console.log('📞 Incoming call received:', callData);
      
      // Get participant info (you might want to fetch from API)
      const participantInfo = {
        name: callData.fromUserName || callData.fromUserId,
        profilePicture: callData.fromUserAvatar || null
      };

      dispatch({
        type: CALL_ACTIONS.SET_INCOMING_CALL,
        payload: { callData, participantInfo }
      });
    };

    // Handle call accepted
    const handleCallAccepted = (callData) => {
      console.log('✅ Call accepted:', callData);
      
      const participantInfo = {
        name: callData.targetUserName || callData.targetUserId,
        profilePicture: callData.targetUserAvatar || null
      };

      dispatch({
        type: CALL_ACTIONS.SET_ACTIVE_CALL,
        payload: { callData, participantInfo }
      });
    };

    // Handle call rejected
    const handleCallRejected = (callData) => {
      console.log('❌ Call rejected:', callData);
      dispatch({ type: CALL_ACTIONS.CLEAR_CALL });
    };

    // Handle call ended
    const handleCallEnded = (callData) => {
      console.log('📴 Call ended:', callData);
      dispatch({ type: CALL_ACTIONS.CLEAR_CALL });
      webRTC.cleanup();
    };

    // Register socket listeners
    socket.on('webrtc_offer', handleIncomingCall);
    socket.on('webrtc_call_accepted', handleCallAccepted);
    socket.on('webrtc_call_rejected', handleCallRejected);
    socket.on('webrtc_call_end', handleCallEnded);

    return () => {
      socket.off('webrtc_offer', handleIncomingCall);
      socket.off('webrtc_call_accepted', handleCallAccepted);
      socket.off('webrtc_call_rejected', handleCallRejected);
      socket.off('webrtc_call_end', handleCallEnded);
    };
  }, [socket, webRTC]);

  // Call Actions
  const startCall = async (targetUserId, callType = 'voice', chatId = null) => {
    try {
      dispatch({ type: CALL_ACTIONS.SET_CALL_STATE, payload: CALL_STATES.OUTGOING });
      
      const result = await webRTC.startCall(targetUserId, callType, chatId);
      
      if (result.success) {
        const participantInfo = {
          name: targetUserId, // You might want to fetch actual user info
          profilePicture: null
        };

        dispatch({
          type: CALL_ACTIONS.SET_ACTIVE_CALL,
          payload: { 
            callData: { 
              callId: result.callId, 
              targetUserId, 
              callType 
            }, 
            participantInfo 
          }
        });
      }
      
      return result;
    } catch (error) {
      console.error('Failed to start call:', error);
      dispatch({ type: CALL_ACTIONS.CLEAR_CALL });
      throw error;
    }
  };

  const acceptCall = async (callData, acceptType = 'voice') => {
    try {
      await webRTC.acceptCall(callData, acceptType);
      
      const participantInfo = {
        name: callData.fromUserName || callData.fromUserId,
        profilePicture: callData.fromUserAvatar || null
      };

      dispatch({
        type: CALL_ACTIONS.SET_ACTIVE_CALL,
        payload: { 
          callData: { ...callData, callType: acceptType }, 
          participantInfo 
        }
      });
    } catch (error) {
      console.error('Failed to accept call:', error);
      dispatch({ type: CALL_ACTIONS.CLEAR_CALL });
      throw error;
    }
  };

  const rejectCall = (callData) => {
    webRTC.rejectCall(callData);
    dispatch({ type: CALL_ACTIONS.CLEAR_CALL });
  };

  const endCall = () => {
    webRTC.endCall();
    dispatch({ type: CALL_ACTIONS.CLEAR_CALL });
  };

  const toggleMute = () => {
    const newMutedState = !state.callControls.isMuted;
    webRTC.toggleMute(newMutedState);
    dispatch({
      type: CALL_ACTIONS.SET_CALL_CONTROLS,
      payload: { isMuted: newMutedState }
    });
  };

  const toggleVideo = () => {
    const newVideoState = !state.callControls.isVideoEnabled;
    webRTC.toggleVideo(newVideoState);
    dispatch({
      type: CALL_ACTIONS.SET_CALL_CONTROLS,
      payload: { isVideoEnabled: newVideoState }
    });
  };

  const toggleSpeaker = () => {
    const newSpeakerState = !state.callControls.isSpeakerOn;
    // Implement speaker toggle logic
    dispatch({
      type: CALL_ACTIONS.SET_CALL_CONTROLS,
      payload: { isSpeakerOn: newSpeakerState }
    });
  };

  const value = {
    // State
    ...state,
    
    // WebRTC streams
    localStream: webRTC.localStream,
    remoteStream: webRTC.remoteStream,
    
    // Actions
    startCall,
    acceptCall,
    rejectCall,
    endCall,
    toggleMute,
    toggleVideo,
    toggleSpeaker
  };

  return (
    <CallContext.Provider value={value}>
      {children}
    </CallContext.Provider>
  );
};

// Hook to use call context
export const useCall = () => {
  const context = useContext(CallContext);
  if (!context) {
    throw new Error('useCall must be used within a CallProvider');
  }
  return context;
};

export default CallContext;
